@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
    text-decoration: none;
}

body {
    background-color: black;
    color: rgb(232, 232, 232); /* Default text color is grey */
    padding: 20px;
    text-align: center;
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: rgba(0, 0, 0, 0.8);
    box-shadow: 0px 4px 10px rgba(255, 255, 255, 0.2);
}

.logo {
    font-size: 2rem;
    font-weight: bold;
    color:grey; 
}

nav {
    display: flex;
    gap: 20px;
}

nav a {
    color: grey; /* Navigation links */
    font-size: 1.5rem;
    transition: 0.3s;
}

nav a:hover {
    color: white;
}

h1, h2, h3 {
    color: #4d4d4d !important; /* Golden-brown shade */
}
h1{
    margin-top: 20px;
}

.menu-toggle {
    display: none;
    font-size: 2rem;
    cursor: pointer;
    color: red;
}

.about-section {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4rem;
    margin-top: 50px;
    text-align: left;
}

.about-section img {
    border-radius: 10px;
    width: 300px;
    height: 300px;
}

footer {
    margin-top: 50px;
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.9);
    color: #AA8C2C;
}

#themeToggle {
    padding: 10px 15px;
    background-color: red;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.light-mode {
    background-color: red;
    color: black;
}

@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    nav {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 60px;
        right: 20px;
        background: black;
        padding: 10px;
    }

    nav.active {
        display: flex;
    }
}

/* Ensure sections are always visible initially */
section {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

section.active {
    opacity: 1;
    transform: translateY(0);
}
