@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
    --primary-color: #00d4ff;
    --secondary-color: #ff6b6b;
    --accent-color: #4ecdc4;
    --text-primary: #ffffff;
    --text-secondary: #b8b8b8;
    --text-muted: #6b7280;
    --bg-primary: #0a0a0a;
    --bg-secondary: #111111;
    --bg-tertiary: #1a1a1a;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --shadow-primary: 0 10px 40px rgba(0, 212, 255, 0.3);
    --shadow-secondary: 0 10px 40px rgba(255, 107, 107, 0.3);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    overflow-x: hidden;
    line-height: 1.6;
}

/* Particle Background */
#particles-js {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 800;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-subtitle {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-weight: 400;
    margin-top: -2px;
}

.nav-menu {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    padding: 0.5rem;
    border-radius: var(--border-radius);
    position: relative;
}

.nav-link i {
    font-size: 1.2rem;
}

.nav-link span {
    font-size: 0.75rem;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    transform: translateY(-2px);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition);
    border-radius: 2px;
}

/* Slide Container */
.slide-container {
    position: relative;
    height: 100vh;
    overflow: hidden;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 2rem;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

.slide.prev {
    transform: translateX(-100%);
}

.slide-content {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding-top: 80px;
}

/* Home Slide */
.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.typing-text {
    position: relative;
}

.typing-text::after {
    content: '|';
    animation: blink 1s infinite;
    color: var(--primary-color);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-muted);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-accent);
    color: white;
    box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 50px rgba(0, 212, 255, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
}

.floating-elements {
    position: absolute;
    top: 50%;
    right: 10%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.floating-icon {
    width: 60px;
    height: 60px;
    background: var(--bg-tertiary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary-color);
    animation: float 3s ease-in-out infinite;
    border: 2px solid rgba(0, 212, 255, 0.3);
}

.floating-icon:nth-child(1) { animation-delay: 0s; }
.floating-icon:nth-child(2) { animation-delay: 0.5s; }
.floating-icon:nth-child(3) { animation-delay: 1s; }
.floating-icon:nth-child(4) { animation-delay: 1.5s; }

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    color: var(--text-muted);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    font-size: 1.5rem;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* About Slide */
.about-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
}

.about-image {
    position: relative;
}

.image-wrapper {
    position: relative;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-primary);
}

.profile-img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: var(--transition);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-accent);
    opacity: 0;
    transition: var(--transition);
}

.image-wrapper:hover .image-overlay {
    opacity: 0.2;
}

.image-wrapper:hover .profile-img {
    transform: scale(1.05);
}

.section-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-description {
    margin-bottom: 2rem;
}

.about-description p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.8;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.skill-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.skill-item:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-primary);
}

.skill-item i {
    font-size: 2rem;
    color: var(--primary-color);
}

.skill-item span {
    font-weight: 600;
    color: var(--text-primary);
}

/* Projects Slide */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.project-card {
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: var(--transition);
}

.project-card:hover::before {
    transform: scaleX(1);
}

.project-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-primary);
}

.project-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 1.5rem;
}

.project-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.project-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.project-tech {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.tech-tag {
    background: rgba(0, 212, 255, 0.1);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    border: 1px solid rgba(0, 212, 255, 0.3);
}

.project-btn {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.project-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Education Slide */
.education-container {
    max-width: 800px;
    margin: 3rem auto 0;
}

.education-card {
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    padding: 3rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 2rem;
    transition: var(--transition);
}

.education-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-primary);
}

.education-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    flex-shrink: 0;
}

.education-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.institution {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.duration {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.education-details p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Contact Slide */
.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.contact-item:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-primary);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.contact-details h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--text-secondary);
}

.contact-form {
    background: var(--bg-tertiary);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-muted);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}
/* Slide Navigation */
.slide-nav {
    position: fixed;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(20px);
    padding: 1rem 2rem;
    border-radius: 50px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
}

.slide-nav-btn {
    width: 40px;
    height: 40px;
    background: transparent;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    color: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide-nav-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.slide-indicators {
    display: flex;
    gap: 0.5rem;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: var(--transition);
}

.indicator.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

.indicator:hover {
    background: var(--primary-color);
    opacity: 0.8;
}

/* Footer */
.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(20px);
    padding: 1rem;
    text-align: center;
    color: var(--text-muted);
    font-size: 0.875rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 999;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-title {
        font-size: 3rem;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .about-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .contact-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .floating-elements {
        display: none;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: rgba(10, 10, 10, 0.95);
        backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 3rem;
        transition: var(--transition);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .education-card {
        flex-direction: column;
        text-align: center;
    }

    .slide-nav {
        bottom: 1rem;
        padding: 0.75rem 1.5rem;
    }

    .slide-nav-btn {
        width: 35px;
        height: 35px;
    }

    .indicator {
        width: 10px;
        height: 10px;
    }
}

@media (max-width: 480px) {
    .slide {
        padding: 1rem;
    }

    .slide-content {
        padding-top: 100px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .nav-container {
        padding: 1rem;
    }

    .project-card,
    .education-card,
    .contact-form {
        padding: 1.5rem;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}

/* Loading Animation */
.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading.fade-out {
    opacity: 0;
    pointer-events: none;
}

.loader {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(0, 212, 255, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-gradient {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.fade-in {
    animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}