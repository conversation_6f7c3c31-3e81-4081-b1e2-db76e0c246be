/* Modern Professional Portfolio CSS */

:root {
    /* Colors */
    --primary-color: #00f5ff;
    --secondary-color: #ff6b6b;
    --accent-color: #4ecdc4;
    --bg-primary: #0a0a0a;
    --bg-secondary: #111111;
    --bg-tertiary: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #b8b8b8;
    --text-muted: #6b7280;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #00f5ff 0%, #4ecdc4 100%);
    --gradient-secondary: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
    --gradient-bg: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);

    /* Shadows */
    --shadow-primary: 0 20px 60px rgba(0, 245, 255, 0.3);
    --shadow-secondary: 0 20px 60px rgba(255, 107, 107, 0.3);
    --shadow-card: 0 25px 50px rgba(0, 0, 0, 0.5);

    /* Transitions */
    --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;

    /* Border Radius */
    --radius: 20px;
    --radius-small: 12px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(78, 205, 196, 0.1) 0%, transparent 50%);
    z-index: -1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -30px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
}

@keyframes themeChange {
    0%, 100% {
        filter: brightness(1) hue-rotate(0deg);
    }
    25% {
        filter: brightness(1.2) hue-rotate(90deg);
    }
    50% {
        filter: brightness(0.8) hue-rotate(180deg);
    }
    75% {
        filter: brightness(1.1) hue-rotate(270deg);
    }
}

/* Glitch Loading Screen */
.glitch-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.8s ease, visibility 0.8s ease;
}

.glitch-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.glitch-container {
    text-align: center;
}

.glitch-text {
    font-size: 4rem;
    font-weight: 800;
    color: var(--primary-color);
    position: relative;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    animation: glitch 2s infinite;
}

.glitch-text::before,
.glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch-text::before {
    animation: glitch-1 2s infinite;
    color: #ff6b6b;
    z-index: -1;
}

.glitch-text::after {
    animation: glitch-2 2s infinite;
    color: #4ecdc4;
    z-index: -2;
}

@keyframes glitch {
    0%, 100% {
        transform: translate(0);
    }
    20% {
        transform: translate(-2px, 2px);
    }
    40% {
        transform: translate(-2px, -2px);
    }
    60% {
        transform: translate(2px, 2px);
    }
    80% {
        transform: translate(2px, -2px);
    }
}

@keyframes glitch-1 {
    0%, 100% {
        transform: translate(0);
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }
    10% {
        transform: translate(-5px, -5px);
        clip-path: polygon(0 0, 100% 0, 100% 35%, 0 35%);
    }
    20% {
        transform: translate(-10px, 5px);
        clip-path: polygon(0 35%, 100% 35%, 100% 70%, 0 70%);
    }
    30% {
        transform: translate(5px, -10px);
        clip-path: polygon(0 70%, 100% 70%, 100% 100%, 0 100%);
    }
    40% {
        transform: translate(-5px, 10px);
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }
}

@keyframes glitch-2 {
    0%, 100% {
        transform: translate(0);
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }
    15% {
        transform: translate(5px, 5px);
        clip-path: polygon(0 0, 100% 0, 100% 30%, 0 30%);
    }
    25% {
        transform: translate(10px, -5px);
        clip-path: polygon(0 30%, 100% 30%, 100% 60%, 0 60%);
    }
    35% {
        transform: translate(-5px, 10px);
        clip-path: polygon(0 60%, 100% 60%, 100% 100%, 0 100%);
    }
    45% {
        transform: translate(5px, -10px);
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }
}

.glitch-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-top: 2rem;
    letter-spacing: 0.3em;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Enhanced Interactive Cursor */
.cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transition: all 0.2s ease;
    mix-blend-mode: difference;
}

.cursor-follower {
    position: fixed;
    width: 40px;
    height: 40px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9997;
    transition: all 0.3s ease;
    opacity: 0.5;
}

.cursor.hover {
    transform: scale(2);
    background: var(--secondary-color);
    box-shadow: 0 0 20px var(--secondary-color);
}

.cursor-follower.hover {
    transform: scale(1.5);
    border-color: var(--secondary-color);
    opacity: 0.8;
}

.cursor.click {
    transform: scale(0.5);
    background: var(--accent-color);
}

.cursor-trail {
    position: fixed;
    width: 6px;
    height: 6px;
    background: var(--primary-color);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9996;
    opacity: 0.6;
    transition: all 0.5s ease;
}

/* Particle Background */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    opacity: 0.3;
    animation: particleFloat 15s infinite linear;
}

.particle:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 12s;
}

.particle:nth-child(2) {
    left: 20%;
    animation-delay: 2s;
    animation-duration: 18s;
}

.particle:nth-child(3) {
    left: 30%;
    animation-delay: 4s;
    animation-duration: 14s;
}

.particle:nth-child(4) {
    left: 40%;
    animation-delay: 6s;
    animation-duration: 16s;
}

.particle:nth-child(5) {
    left: 50%;
    animation-delay: 8s;
    animation-duration: 20s;
}

.particle:nth-child(6) {
    left: 60%;
    animation-delay: 1s;
    animation-duration: 13s;
}

.particle:nth-child(7) {
    left: 70%;
    animation-delay: 3s;
    animation-duration: 17s;
}

.particle:nth-child(8) {
    left: 80%;
    animation-delay: 5s;
    animation-duration: 15s;
}

.particle:nth-child(9) {
    left: 90%;
    animation-delay: 7s;
    animation-duration: 19s;
}

.particle:nth-child(10) {
    left: 15%;
    animation-delay: 9s;
    animation-duration: 11s;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.3;
    }
    90% {
        opacity: 0.3;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-dot {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: var(--transition);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.theme-toggle {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.theme-toggle:hover {
    background: rgba(0, 245, 255, 0.2);
    transform: rotate(180deg);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition);
    border-radius: 2px;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: var(--gradient-primary);
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.profile-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.profile-image {
    position: relative;
    width: 350px;
    height: 350px;
    border-radius: 50%;
    overflow: hidden;
    border: 6px solid var(--primary-color);
    box-shadow: var(--shadow-primary);
    transition: var(--transition);
}

.profile-image:hover {
    transform: scale(1.05);
    box-shadow: 0 30px 80px rgba(0, 245, 255, 0.4);
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.profile-image:hover img {
    transform: scale(1.1);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition);
}

.profile-image:hover .image-overlay {
    opacity: 0.3;
}



.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: var(--text-muted);
    animation: bounce 2s infinite;
}

.scroll-text {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.scroll-arrow {
    font-size: 1.2rem;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Section Styles - Overlapping Effect */
section {
    padding: 6rem 0;
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    z-index: 1;
}

section:not(.hero) {
    position: sticky;
    top: 0;
    background: var(--bg-primary);
    border-radius: 30px 30px 0 0;
    margin-top: -30px;
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.5);
    transition: var(--transition);
}

section:not(.hero)::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 245, 255, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: 30px 30px 0 0;
}

section:not(.hero).section-entering::before {
    opacity: 1;
    animation: sectionGlitch 0.6s ease-in-out;
}

@keyframes sectionGlitch {
    0%, 100% {
        transform: translateX(0) skewX(0deg);
        filter: hue-rotate(0deg);
    }
    20% {
        transform: translateX(-5px) skewX(2deg);
        filter: hue-rotate(90deg);
    }
    40% {
        transform: translateX(5px) skewX(-2deg);
        filter: hue-rotate(180deg);
    }
    60% {
        transform: translateX(-3px) skewX(1deg);
        filter: hue-rotate(270deg);
    }
    80% {
        transform: translateX(3px) skewX(-1deg);
        filter: hue-rotate(360deg);
    }
}

section:nth-child(even) {
    background: var(--bg-secondary);
}

/* Section stacking order */
.hero { z-index: 1; }
.about { z-index: 2; }
.skills { z-index: 3; }
.projects { z-index: 4; }
.contact { z-index: 5; }

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-number {
    display: inline-block;
    font-size: 1rem;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
    padding: 0.5rem 1rem;
    background: rgba(0, 245, 255, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(0, 245, 255, 0.3);
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    background: var(--bg-secondary);
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.text-block {
    margin-bottom: 2rem;
}

.text-block h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.text-block p {
    color: var(--text-secondary);
    line-height: 1.8;
    font-size: 1.1rem;
}

.qualities {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.quality {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-small);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.quality:hover {
    background: rgba(0, 245, 255, 0.1);
    border-color: var(--primary-color);
    transform: translateX(10px);
}

.quality i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.quality span {
    color: var(--text-secondary);
    font-weight: 500;
}

.experience-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: var(--radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem;
    box-shadow: var(--shadow-card);
    transition: var(--transition);
}

.experience-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-primary);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.card-header h4 {
    font-size: 1.3rem;
    color: var(--primary-color);
    font-weight: 600;
}

.year {
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

.card-content h5 {
    font-size: 1.1rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.card-content p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width 1s ease;
    position: relative;
    overflow: hidden;
}

.progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

.progress.glitch {
    animation: progressGlitch 0.5s ease-in-out;
}

.progress-text {
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* Skills Section */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.skill-category {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: var(--radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem;
    box-shadow: var(--shadow-card);
    transition: var(--transition);
}

.skill-category:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-primary);
}

.skill-category h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
    font-weight: 600;
    text-align: center;
}

.skill-items {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.skill-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.skill-icon {
    width: 50px;
    height: 50px;
    background: rgba(0, 245, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(0, 245, 255, 0.3);
    transition: var(--transition);
}

.skill-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.skill-info {
    flex: 1;
}

.skill-name {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.skill-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 3px;
    width: 0%;
    transition: width 1.5s ease;
    position: relative;
    overflow: hidden;
}

.skill-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

.skill-progress.glitch {
    animation: progressGlitch 0.5s ease-in-out;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes progressGlitch {
    0%, 100% {
        transform: scaleX(1);
        filter: hue-rotate(0deg);
    }
    10% {
        transform: scaleX(1.1) skewX(5deg);
        filter: hue-rotate(90deg);
    }
    20% {
        transform: scaleX(0.9) skewX(-5deg);
        filter: hue-rotate(180deg);
    }
    30% {
        transform: scaleX(1.05) skewX(3deg);
        filter: hue-rotate(270deg);
    }
    40% {
        transform: scaleX(0.95) skewX(-3deg);
        filter: hue-rotate(360deg);
    }
    50% {
        transform: scaleX(1.02) skewX(2deg);
        filter: hue-rotate(45deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: rgba(10, 10, 10, 0.95);
        backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 3rem;
        transition: var(--transition);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .name {
        font-size: 3rem;
    }

    .hero-stats {
        justify-content: center;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .profile-image {
        width: 280px;
        height: 280px;
        border-width: 4px;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-buttons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .name {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .profile-card {
        width: 280px;
        height: 350px;
        padding: 1.5rem;
    }

    .profile-image {
        width: 250px;
        height: 250px;
        border-width: 4px;
    }
}

/* Projects Section */
.projects {
    background: var(--bg-secondary);
}

.projects-grid {
    display: flex;
    justify-content: center;
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.project-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: var(--radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
    box-shadow: var(--shadow-card);
    transition: var(--transition);
    perspective: 1000px;
    position: relative;
    height: 500px;
}

.project-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.8s;
    transform-style: preserve-3d;
}

.project-card:hover .project-card-inner {
    transform: rotateY(180deg);
}

.project-card-front,
.project-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: var(--radius);
    display: flex;
    flex-direction: column;
}

.project-card-back {
    transform: rotateY(180deg);
    background: rgba(0, 245, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid var(--primary-color);
    padding: 2rem;
    justify-content: center;
    align-items: center;
}

.project-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-primary);
}

.project-card-back h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.feature-list {
    list-style: none;
    margin-bottom: 2rem;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    font-size: 1rem;
}

.feature-list i {
    color: var(--primary-color);
    font-size: 1rem;
}

.project-stats {
    display: flex;
    gap: 2rem;
    justify-content: center;
}

.project-stats .stat {
    text-align: center;
}

.project-stats .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.project-stats .stat-label {
    font-size: 0.8rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.project-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1.5rem;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    font-size: 0.9rem;
}

.action-btn.primary {
    background: var(--primary-color);
    color: white;
}

.action-btn.primary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.action-btn.secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.action-btn.secondary:hover {
    background: var(--primary-color);
    color: white;
}

.project-image {
    height: 200px;
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.project-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    min-width: 140px;
    justify-content: center;
}

.live-demo {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-primary);
}

.live-demo:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 25px 70px rgba(0, 245, 255, 0.4);
}

.see-code {
    background: rgba(255, 255, 255, 0.9);
    color: var(--text-primary);
    border: 2px solid var(--primary-color);
}

.see-code:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.project-content {
    padding: 2rem;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.project-header h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    font-weight: 600;
}

.project-status {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.project-status {
    background: rgba(78, 205, 196, 0.2);
    color: #4ecdc4;
    border: 1px solid rgba(78, 205, 196, 0.3);
}

.project-status.coming-soon {
    background: rgba(255, 107, 107, 0.2);
    color: #ff6b6b;
    border: 1px solid rgba(255, 107, 107, 0.3);
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.project-tech {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tech-tag {
    background: rgba(0, 245, 255, 0.1);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    border: 1px solid rgba(0, 245, 255, 0.3);
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: var(--radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-card);
    transition: var(--transition);
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-primary);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.contact-details h4 {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.contact-details p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.contact-form {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-radius: var(--radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem;
    box-shadow: var(--shadow-card);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-small);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 245, 255, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-muted);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background: var(--bg-secondary);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-text p {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow-primary);
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .projects-grid {
        max-width: 100%;
        padding: 0 1rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .project-card {
        margin: 0 1rem;
    }
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    top: 10%;
    right: 30%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.hero-text {
    z-index: 2;
}

.greeting {
    display: block;
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 400;
}

.name {
    display: block;
    font-size: 4rem;
    font-weight: 800;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    line-height: 1.1;
}

.role {
    display: block;
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-weight: 500;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    line-height: 1.8;
    max-width: 500px;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: var(--radius-small);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-primary);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 25px 70px rgba(0, 245, 255, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--bg-primary);
    transform: translateY(-3px);
}
