/* Authentic Portfolio - Human-Made Feel */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-white: #ffffff;
    --bg-gray: #f9fafb;
    --bg-light: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.nav-link:hover {
    color: var(--primary-color);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, var(--bg-white) 0%, var(--bg-gray) 100%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 3rem;
    align-items: center;
}

.hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 500;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
}

.profile-pic {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
}

/* Sections */
section {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.section-subtitle {
    text-align: center;
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 3rem;
}

/* About Section */
.about {
    background: var(--bg-gray);
}

.about-text {
    max-width: 800px;
    margin: 0 auto;
}

.about-text p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.skills-simple {
    margin: 2rem 0;
}

.skills-simple h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.skill-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.currently-learning {
    margin-top: 2rem;
}

.currently-learning h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.currently-learning ul {
    list-style: none;
}

.currently-learning li {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Projects Section */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.project-card {
    background: var(--bg-white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.project-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.project-header h3 {
    font-size: 1.4rem;
    color: var(--text-primary);
}

.project-links {
    display: flex;
    gap: 0.5rem;
}

.project-links a {
    color: var(--text-light);
    font-size: 1.2rem;
    transition: color 0.2s ease;
}

.project-links a:hover {
    color: var(--primary-color);
}

.project-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.project-tech {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tech-tag {
    background: var(--bg-light);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.project-learnings {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-style: italic;
    border-left: 3px solid var(--primary-color);
    padding-left: 1rem;
}

.future-projects {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
    background: var(--bg-light);
    border-radius: 12px;
}

.future-projects h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.future-projects p {
    color: var(--text-secondary);
}

/* Contact Section */
.contact {
    background: var(--bg-gray);
}

.contact-content {
    max-width: 600px;
    margin: 0 auto;
}

.contact-info {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-white);
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    width: 30px;
}

.contact-item h4 {
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.contact-item p {
    color: var(--text-secondary);
    margin: 0;
}

.contact-note {
    background: var(--bg-white);
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.contact-note p {
    color: var(--text-secondary);
    margin: 0;
}

/* Footer */
.footer {
    background: var(--text-primary);
    color: white;
    text-align: center;
    padding: 2rem 0;
}

.footer p {
    margin: 0;
    color: var(--text-light);
}

/* Responsive */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--bg-white);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: left 0.3s ease;
        border-top: 1px solid var(--border-color);
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .projects-grid {
        grid-template-columns: 1fr;
    }
    
    .hero-buttons {
        justify-content: center;
    }
}
