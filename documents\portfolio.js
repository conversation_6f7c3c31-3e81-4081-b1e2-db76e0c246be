// Portfolio Interactive Features
class PortfolioSlider {
    constructor() {
        this.currentSlide = 0;
        this.slides = document.querySelectorAll('.slide');
        this.indicators = document.querySelectorAll('.indicator');
        this.navLinks = document.querySelectorAll('.nav-link');
        this.totalSlides = this.slides.length;
        this.isAnimating = false;

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initParticles();
        this.startTypingAnimation();
        this.setupIntersectionObserver();
        this.preloadImages();
    }

    setupEventListeners() {
        // Navigation links
        this.navLinks.forEach((link, index) => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.goToSlide(index);
            });
        });

        // Slide indicators
        this.indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => {
                this.goToSlide(index);
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') this.prevSlide();
            if (e.key === 'ArrowRight') this.nextSlide();
            if (e.key >= '1' && e.key <= '5') {
                this.goToSlide(parseInt(e.key) - 1);
            }
        });

        // Touch/swipe support
        this.setupTouchEvents();

        // Mobile menu toggle
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');

        if (hamburger && navMenu) {
            hamburger.addEventListener('click', () => {
                hamburger.classList.toggle('active');
                navMenu.classList.toggle('active');
            });
        }

        // Form submission
        const contactForm = document.querySelector('.contact-form form');
        if (contactForm) {
            contactForm.addEventListener('submit', this.handleFormSubmit.bind(this));
        }

        // Scroll indicator click
        const scrollIndicator = document.querySelector('.scroll-indicator');
        if (scrollIndicator) {
            scrollIndicator.addEventListener('click', () => {
                this.nextSlide();
            });
        }
    }

    setupTouchEvents() {
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;

            const deltaX = endX - startX;
            const deltaY = endY - startY;

            // Only trigger if horizontal swipe is more significant than vertical
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                    this.prevSlide();
                } else {
                    this.nextSlide();
                }
            }
        });
    }

    goToSlide(slideIndex) {
        if (this.isAnimating || slideIndex === this.currentSlide || slideIndex < 0 || slideIndex >= this.totalSlides) {
            return;
        }

        this.isAnimating = true;

        // Update slides
        this.slides[this.currentSlide].classList.remove('active');
        this.slides[slideIndex].classList.add('active');

        // Update indicators
        this.indicators[this.currentSlide].classList.remove('active');
        this.indicators[slideIndex].classList.add('active');

        // Update navigation
        this.navLinks[this.currentSlide].classList.remove('active');
        this.navLinks[slideIndex].classList.add('active');

        this.currentSlide = slideIndex;

        // Add animation class
        this.slides[this.currentSlide].classList.add('fade-in');

        // Reset animation flag
        setTimeout(() => {
            this.isAnimating = false;
            this.slides[this.currentSlide].classList.remove('fade-in');
        }, 800);

        // Trigger slide-specific animations
        this.triggerSlideAnimations(slideIndex);
    }

    nextSlide() {
        const nextIndex = (this.currentSlide + 1) % this.totalSlides;
        this.goToSlide(nextIndex);
    }

    prevSlide() {
        const prevIndex = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
        this.goToSlide(prevIndex);
    }

    triggerSlideAnimations(slideIndex) {
        const slide = this.slides[slideIndex];
        const animatedElements = slide.querySelectorAll('.skill-item, .project-card, .contact-item, .education-card');

        animatedElements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(30px)';

            setTimeout(() => {
                element.style.transition = 'all 0.6s ease';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 100 + 200);
        });
    }

    startTypingAnimation() {
        const typingText = document.querySelector('.typing-text');
        if (!typingText) return;

        const text = "Hello, I'm Jaianish";
        const speed = 100;
        let i = 0;

        typingText.textContent = '';

        function typeWriter() {
            if (i < text.length) {
                typingText.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, speed);
            }
        }

        setTimeout(typeWriter, 1000);
    }

    initParticles() {
        if (typeof particlesJS !== 'undefined') {
            particlesJS('particles-js', {
                particles: {
                    number: {
                        value: 80,
                        density: {
                            enable: true,
                            value_area: 800
                        }
                    },
                    color: {
                        value: '#00d4ff'
                    },
                    shape: {
                        type: 'circle',
                        stroke: {
                            width: 0,
                            color: '#000000'
                        }
                    },
                    opacity: {
                        value: 0.5,
                        random: false,
                        anim: {
                            enable: false,
                            speed: 1,
                            opacity_min: 0.1,
                            sync: false
                        }
                    },
                    size: {
                        value: 3,
                        random: true,
                        anim: {
                            enable: false,
                            speed: 40,
                            size_min: 0.1,
                            sync: false
                        }
                    },
                    line_linked: {
                        enable: true,
                        distance: 150,
                        color: '#00d4ff',
                        opacity: 0.4,
                        width: 1
                    },
                    move: {
                        enable: true,
                        speed: 6,
                        direction: 'none',
                        random: false,
                        straight: false,
                        out_mode: 'out',
                        bounce: false,
                        attract: {
                            enable: false,
                            rotateX: 600,
                            rotateY: 1200
                        }
                    }
                },
                interactivity: {
                    detect_on: 'canvas',
                    events: {
                        onhover: {
                            enable: true,
                            mode: 'repulse'
                        },
                        onclick: {
                            enable: true,
                            mode: 'push'
                        },
                        resize: true
                    },
                    modes: {
                        grab: {
                            distance: 400,
                            line_linked: {
                                opacity: 1
                            }
                        },
                        bubble: {
                            distance: 400,
                            size: 40,
                            duration: 2,
                            opacity: 8,
                            speed: 3
                        },
                        repulse: {
                            distance: 200,
                            duration: 0.4
                        },
                        push: {
                            particles_nb: 4
                        },
                        remove: {
                            particles_nb: 2
                        }
                    }
                },
                retina_detect: true
            });
        }
    }

    setupIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, observerOptions);

        // Observe all animated elements
        document.querySelectorAll('.skill-item, .project-card, .contact-item, .education-card').forEach(el => {
            observer.observe(el);
        });
    }

    preloadImages() {
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            const imageUrl = img.src;
            const image = new Image();
            image.src = imageUrl;
        });
    }

    handleFormSubmit(e) {
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        submitBtn.disabled = true;

        // Simulate form submission (replace with actual form handling)
        setTimeout(() => {
            // Reset form
            form.reset();

            // Show success message
            submitBtn.innerHTML = '<i class="fas fa-check"></i> Message Sent!';
            submitBtn.style.background = '#4ecdc4';

            // Reset button after 3 seconds
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                submitBtn.style.background = '';
            }, 3000);
        }, 2000);
    }
}

// Global functions for button clicks
function nextSlide() {
    if (window.portfolioSlider) {
        window.portfolioSlider.nextSlide();
    }
}

function prevSlide() {
    if (window.portfolioSlider) {
        window.portfolioSlider.prevSlide();
    }
}

function goToSlide(index) {
    if (window.portfolioSlider) {
        window.portfolioSlider.goToSlide(index);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Show loading screen
    const loading = document.createElement('div');
    loading.className = 'loading';
    loading.innerHTML = '<div class="loader"></div>';
    document.body.appendChild(loading);

    // Initialize portfolio slider
    window.portfolioSlider = new PortfolioSlider();

    // Hide loading screen after initialization
    setTimeout(() => {
        loading.classList.add('fade-out');
        setTimeout(() => {
            loading.remove();
        }, 500);
    }, 1500);

    // Add smooth scrolling for any remaining anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add parallax effect to floating elements
    window.addEventListener('mousemove', (e) => {
        const floatingElements = document.querySelectorAll('.floating-icon');
        const x = e.clientX / window.innerWidth;
        const y = e.clientY / window.innerHeight;

        floatingElements.forEach((element, index) => {
            const speed = (index + 1) * 0.5;
            const xPos = (x - 0.5) * speed * 20;
            const yPos = (y - 0.5) * speed * 20;

            element.style.transform = `translate(${xPos}px, ${yPos}px)`;
        });
    });

    // Add dynamic background gradient based on current slide
    const updateBackgroundGradient = () => {
        const currentSlide = window.portfolioSlider?.currentSlide || 0;
        const gradients = [
            'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.3) 0%, transparent 50%)',
            'radial-gradient(circle at 40% 40%, rgba(78, 205, 196, 0.3) 0%, transparent 50%), radial-gradient(circle at 60% 60%, rgba(0, 212, 255, 0.3) 0%, transparent 50%)',
            'radial-gradient(circle at 70% 30%, rgba(255, 107, 107, 0.3) 0%, transparent 50%), radial-gradient(circle at 30% 70%, rgba(120, 119, 198, 0.3) 0%, transparent 50%)',
            'radial-gradient(circle at 50% 20%, rgba(78, 205, 196, 0.3) 0%, transparent 50%), radial-gradient(circle at 50% 80%, rgba(0, 212, 255, 0.3) 0%, transparent 50%)',
            'radial-gradient(circle at 80% 80%, rgba(255, 107, 107, 0.3) 0%, transparent 50%), radial-gradient(circle at 20% 20%, rgba(78, 205, 196, 0.3) 0%, transparent 50%)'
        ];

        document.body.style.background = `${gradients[currentSlide]}, #0a0a0a`;
    };

    // Update background on slide change
    setInterval(updateBackgroundGradient, 100);
});
