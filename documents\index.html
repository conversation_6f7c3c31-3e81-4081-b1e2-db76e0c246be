<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="portfolio.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <title>Jaianish Portfolio</title>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">
                <span class="logo-text">Jaianish</span>
                <span class="logo-subtitle">Frontend Developer</span>
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link active" data-slide="0">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
                <a href="#about" class="nav-link" data-slide="1">
                    <i class="fas fa-user"></i>
                    <span>About</span>
                </a>
                <a href="#projects" class="nav-link" data-slide="2">
                    <i class="fas fa-code"></i>
                    <span>Projects</span>
                </a>
                <a href="#education" class="nav-link" data-slide="3">
                    <i class="fas fa-graduation-cap"></i>
                    <span>Education</span>
                </a>
                <a href="#contact" class="nav-link" data-slide="4">
                    <i class="fas fa-envelope"></i>
                    <span>Contact</span>
                </a>
            </div>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Slide Container -->
    <div class="slide-container">
        <!-- Slide 1: Home -->
        <section class="slide active" id="home" data-slide="0">
            <div class="slide-content">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <span class="typing-text">Jaianish</span>
                    </h1>
                    <div class="hero-welcome">
                        <h2 class="welcome-text">Welcome to My Portfolio</h2>
                    </div>
                    <div class="hero-description">
                        <p>Aspiring Frontend Developer specializing in modern web technologies. I craft responsive, user-centric websites with clean code and attention to detail. Currently pursuing Computer Science Engineering while building practical web solutions.</p>
                    </div>
                    <div class="hero-highlights">
                        <div class="highlight-item">
                            <i class="fas fa-code"></i>
                            <span>Clean Code</span>
                        </div>
                        <div class="highlight-item">
                            <i class="fas fa-mobile-alt"></i>
                            <span>Responsive Design</span>
                        </div>
                        <div class="highlight-item">
                            <i class="fas fa-rocket"></i>
                            <span>Modern Tech</span>
                        </div>
                        <div class="highlight-item">
                            <i class="fas fa-lightbulb"></i>
                            <span>Problem Solver</span>
                        </div>
                    </div>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <div class="stat-number">2+</div>
                            <div class="stat-label">Projects Completed</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">3</div>
                            <div class="stat-label">Technologies Mastered</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">Commitment</div>
                        </div>
                    </div>
                    <div class="hero-buttons">
                        <button class="btn btn-primary" onclick="nextSlide()">
                            <i class="fas fa-rocket"></i>
                            Explore My Work
                        </button>
                        <button class="btn btn-secondary" onclick="goToSlide(4)">
                            <i class="fas fa-paper-plane"></i>
                            Get In Touch
                        </button>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="floating-elements">
                        <div class="floating-icon"><i class="fab fa-html5"></i></div>
                        <div class="floating-icon"><i class="fab fa-css3-alt"></i></div>
                        <div class="floating-icon"><i class="fab fa-js-square"></i></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Slide 2: About -->
        <section class="slide" id="about" data-slide="1">
            <div class="slide-content">
                <div class="about-container">
                    <div class="about-image">
                        <div class="image-wrapper">
                            <img src="../images/jaianish.jpg" alt="Jaianish" class="profile-img">
                            <div class="image-overlay"></div>
                        </div>
                    </div>
                    <div class="about-text">
                        <h2 class="section-title">About Me</h2>
                        <div class="about-description">
                            <p>I am Jaianish Jayabharath, a dedicated Computer Science Engineering student with a passion for frontend development. Currently in my first year at Sri Eshwar College of Engineering, I combine academic learning with hands-on project experience.</p>
                            <p>My approach to web development focuses on creating clean, efficient code while ensuring exceptional user experiences. I believe in continuous learning and staying updated with the latest web technologies and best practices.</p>
                            <p>When I'm not coding, I enjoy exploring new design trends, contributing to open-source projects, and solving complex programming challenges that help me grow as a developer.</p>
                        </div>
                        <div class="about-qualities">
                            <div class="quality-item">
                                <h4>Problem Solving</h4>
                                <p>Analytical thinking and creative solutions</p>
                            </div>
                            <div class="quality-item">
                                <h4>Attention to Detail</h4>
                                <p>Pixel-perfect designs and clean code</p>
                            </div>
                            <div class="quality-item">
                                <h4>Continuous Learning</h4>
                                <p>Always exploring new technologies</p>
                            </div>
                        </div>
                        <div class="skills-grid">
                            <div class="skill-item">
                                <i class="fab fa-html5"></i>
                                <span>HTML5</span>
                            </div>
                            <div class="skill-item">
                                <i class="fab fa-css3-alt"></i>
                                <span>CSS3</span>
                            </div>
                            <div class="skill-item">
                                <i class="fab fa-js-square"></i>
                                <span>JavaScript</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Slide 3: Projects -->
        <section class="slide" id="projects" data-slide="2">
            <div class="slide-content">
                <h2 class="section-title">Featured Projects</h2>
                <p class="section-subtitle">A showcase of my recent work and technical capabilities</p>
                <div class="projects-grid">
                    <div class="project-card featured">
                        <div class="project-header">
                            <div class="project-icon">
                                <i class="fas fa-coffee"></i>
                            </div>
                            <div class="project-status">
                                <span class="status-badge completed">Completed</span>
                            </div>
                        </div>
                        <h3>Coffee Shop Website</h3>
                        <p>A fully responsive coffee shop website featuring modern design principles, interactive menu sections, and smooth animations. Implemented mobile-first approach with CSS Grid and Flexbox for optimal user experience across all devices.</p>
                        <div class="project-features">
                            <div class="feature">✓ Responsive Design</div>
                            <div class="feature">✓ Interactive Menu</div>
                            <div class="feature">✓ Smooth Animations</div>
                            <div class="feature">✓ Mobile Optimized</div>
                        </div>
                        <div class="project-tech">
                            <span class="tech-tag">HTML5</span>
                            <span class="tech-tag">CSS3</span>
                            <span class="tech-tag">JavaScript</span>
                            <span class="tech-tag">Responsive</span>
                        </div>
                        <div class="project-links">
                            <button class="project-btn primary">
                                <i class="fas fa-external-link-alt"></i>
                                Live Demo
                            </button>
                            <button class="project-btn secondary">
                                <i class="fab fa-github"></i>
                                Source Code
                            </button>
                        </div>
                    </div>
                    <div class="project-card featured">
                        <div class="project-header">
                            <div class="project-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="project-status">
                                <span class="status-badge completed">Completed</span>
                            </div>
                        </div>
                        <h3>Task Management App</h3>
                        <p>A comprehensive to-do list application with advanced features including task categorization, priority levels, due dates, and local storage persistence. Built with vanilla JavaScript focusing on clean code architecture.</p>
                        <div class="project-features">
                            <div class="feature">✓ CRUD Operations</div>
                            <div class="feature">✓ Local Storage</div>
                            <div class="feature">✓ Task Categories</div>
                            <div class="feature">✓ Priority System</div>
                        </div>
                        <div class="project-tech">
                            <span class="tech-tag">HTML5</span>
                            <span class="tech-tag">CSS3</span>
                            <span class="tech-tag">JavaScript</span>
                            <span class="tech-tag">LocalStorage</span>
                        </div>
                        <div class="project-links">
                            <button class="project-btn primary">
                                <i class="fas fa-external-link-alt"></i>
                                Live Demo
                            </button>
                            <button class="project-btn secondary">
                                <i class="fab fa-github"></i>
                                Source Code
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Slide 4: Education -->
        <section class="slide" id="education" data-slide="3">
            <div class="slide-content">
                <h2 class="section-title">Education</h2>
                <div class="education-container">
                    <div class="education-card">
                        <div class="education-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="education-content">
                            <h3>B.E  Computer Science Engineering</h3>
                            <p class="institution">Sri Eshwar College of Engineering, Coimbatore</p>
                            <p class="duration">2024 - 2028</p>
                            <div class="education-details">
                                <p>Currently pursuing my undergraduate degree with a focus on software development, web technologies, and computer science fundamentals.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Slide 5: Contact -->
        <section class="slide" id="contact" data-slide="4">
            <div class="slide-content">
                <h2 class="section-title">Get In Touch</h2>
                <div class="contact-container">
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <h3>Email</h3>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fab fa-linkedin"></i>
                            </div>
                            <div class="contact-details">
                                <h3>LinkedIn</h3>
                                <p>Jaianish Jayabharath</p>
                            </div>
                        </div>
                    </div>
                    <div class="contact-form">
                        <form>
                            <div class="form-group">
                                <input type="text" placeholder="Your Name" required>
                            </div>
                            <div class="form-group">
                                <input type="email" placeholder="Your Email" required>
                            </div>
                            <div class="form-group">
                                <textarea placeholder="Your Message" rows="5" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                Send Message
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </div>



    <!-- Footer -->
    <footer class="footer">
        <p>&copy; 2025 Jaianish. All rights reserved.</p>
    </footer>

    <script src="portfolio.js"></script>
</body>
</html>
